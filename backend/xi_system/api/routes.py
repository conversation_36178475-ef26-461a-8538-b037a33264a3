# xi_system/api/routes.py

"""
API routes for Xi Intelligent Agent System.

This module contains all FastAPI route handlers for the Xi ContextOS,
including the main chat endpoint and health checks.
"""

from fastapi import APIRouter, Depends, HTTPException, status, WebSocket, WebSocketDisconnect

import logging
import uuid
import json
from typing import Dict, Any

from ..core.xi_core import Xi<PERSON>ore
from ..service import initialize_services
from .models import HealthResponse, HistoryResponse
from .connection_manager import manager as connection_manager
from .protocol_utils import (
    create_protocol_message,
    create_stream_start_message,
    create_stream_end_message,
    create_error_message,
    parse_protocol_message
)

# Set up logging
logger = logging.getLogger(__name__)

# Create an APIRouter instance for modular route organization
router = APIRouter()

# Global XiCore instance (singleton pattern for V0.83)
_xi_core_instance = None
_service_container = None

def get_xi_core() -> XiCore:
    """
    Dependency function to provide XiCore instance.

    Uses singleton pattern to maintain a single XiCore instance
    across all requests for optimal resource usage.
    V0.83: Now uses ServiceContainer for dependency injection.

    Returns:
        XiCore: Initialized XiCore instance
    """
    global _xi_core_instance, _service_container

    try:
        # Return existing instance if available
        if _xi_core_instance is not None:
            return _xi_core_instance

        logger.info("Initializing XiCore singleton instance with ServiceContainer")

        # Initialize service container (only once)
        if _service_container is None:
            _service_container = initialize_services()
            logger.info("Service container initialized")
        else:
            logger.info("Using existing service container")

        # Initialize XiCore with dependency injection
        _xi_core_instance = XiCore(_service_container)
        logger.info("XiCore singleton instance created")

        return _xi_core_instance

    except Exception as e:
        logger.error(f"Failed to initialize XiCore: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to initialize Xi ContextOS"
        )








@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Health check endpoint",
    description="Check the health status of the Xi ContextOS"
)
async def health_check(xi_core: XiCore = Depends(get_xi_core)) -> HealthResponse:
    """
    Health check endpoint to verify system status.

    Args:
        xi_core: The XiCore instance (injected dependency)

    Returns:
        HealthResponse: Current system health status
    """
    try:
        # Perform comprehensive health check
        health_status = xi_core.get_health_status()

        return HealthResponse(
            status=health_status["status"],
            version="0.83.0",
            message="Xi ContextOS V0.83 with Architecture Purification is ready to serve."
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Health check failed"
        )


@router.get(
    "/status",
    summary="System status endpoint",
    description="Get detailed system status information including memory statistics"
)
async def system_status(xi_core: XiCore = Depends(get_xi_core)) -> Dict[str, Any]:
    """
    Get detailed system status including memory and component statistics.

    Args:
        xi_core: The XiCore instance (injected dependency)

    Returns:
        Dict containing comprehensive system status information
    """
    try:
        # Get comprehensive system statistics
        system_stats = xi_core.get_stats()
        health_status = xi_core.get_health_status()

        return {
            "status": "operational",
            "version": "0.83.0",
            "core_status": health_status["status"],
            "system_stats": system_stats,
            "features": [
                "context_os",
                "memorizz_enhanced_memory",
                "multi_dimensional_memory_signals",
                "structured_xml_prompts",
                "mongodb_vector_search",
                "persona_management"
            ]
        }
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system status"
        )


@router.get(
    "/api/v1/history/latest",
    response_model=HistoryResponse,
    summary="Get latest conversation history",
    description="Retrieve the most recent conversation messages with pagination support"
)
async def get_latest_history(
    limit: int = 20,
    session_id: str = None,
    xi_core: XiCore = Depends(get_xi_core)
) -> HistoryResponse:
    """
    Get latest conversation history for frontend initialization.

    Args:
        limit: Maximum number of messages to retrieve (default: 20)
        session_id: Optional session identifier to filter by
        xi_core: The XiCore instance (injected dependency)

    Returns:
        HistoryResponse: Messages list and pagination info
    """
    try:
        # Get history data from XiCore
        history_data = xi_core.get_latest_history(limit=limit, session_id=session_id)

        return HistoryResponse(
            messages=history_data["messages"],
            has_more=history_data["has_more"]
        )

    except Exception as e:
        logger.error(f"Failed to get latest history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation history"
        )


@router.get(
    "/memory/search",
    summary="Memory search endpoint",
    description="Search memories using semantic similarity"
)
async def search_memories(
    query: str,
    limit: int = 5,
    xi_core: XiCore = Depends(get_xi_core)
) -> Dict[str, Any]:
    """
    Search memories using semantic similarity.

    Args:
        query: Search query string
        limit: Maximum number of results to return
        xi_core: The XiCore instance (injected dependency)

    Returns:
        Dict containing search results
    """
    try:
        memories = xi_core.search_memories(query, limit)

        return {
            "query": query,
            "results_count": len(memories),
            "memories": memories
        }
    except Exception as e:
        logger.error(f"Memory search failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Memory search failed"
        )


@router.websocket("/ws/chat")
async def websocket_endpoint(
    websocket: WebSocket,
    xi_core: XiCore = Depends(get_xi_core)
):
    """
    WebSocket聊天端点。
    处理实时的、双向的聊天交互。

    Args:
        websocket: WebSocket连接对象
        xi_core: XiCore实例（依赖注入）

    Note:
        客户端应发送JSON格式的消息：
        {"yu_input": "用户输入", "session_id": "可选的会话ID"}

        服务器将流式返回Xi的回复，并以[STREAM_END]信号结束。
    """
    client_id = str(uuid.uuid4())
    await connection_manager.connect(websocket, client_id)

    try:
        while True:
            # 1. 等待并接收来自客户端的消息
            data = await websocket.receive_text()

            # 解析JSON数据
            try:
                request_data = json.loads(data)
                yu_input = request_data.get("yu_input")
                session_id = request_data.get("session_id") or str(uuid.uuid4())
            except json.JSONDecodeError:
                error_message = create_error_message("无效的JSON格式")
                await connection_manager.send_personal_message(error_message, client_id)
                continue

            if not yu_input:
                error_message = create_error_message("'yu_input'字段不能为空")
                await connection_manager.send_personal_message(error_message, client_id)
                continue

            logger.info(f"WebSocket [{client_id}] received: {yu_input[:50]}...")

            # 为本次响应流生成唯一ID
            response_message_id = str(uuid.uuid4())

            # 发送 stream_start 事件
            input_message_id = str(uuid.uuid4())  # 用户消息ID的占位符
            start_message = create_stream_start_message(session_id, input_message_id, response_message_id)
            await connection_manager.send_personal_message(start_message, client_id)

            # 调用XiCore的核心流式处理逻辑
            response_generator = xi_core.run_stream(
                yu_input=yu_input,
                session_id=session_id
            )

            # 处理来自后端的流式事件
            for chunk_json in response_generator:
                try:
                    # 解析来自 agentic_loop 的事件
                    event_data = parse_protocol_message(chunk_json)
                    event_type = event_data.get("type")
                    payload = event_data.get("payload")

                    # 重新封装并使用本次流的 message_id 发送
                    protocol_message = create_protocol_message(event_type, payload, response_message_id)
                    await connection_manager.send_personal_message(protocol_message, client_id)

                except (json.JSONDecodeError, KeyError) as e:
                    # 处理非协议消息（向后兼容或错误情况）
                    logger.warning(f"Received non-protocol message: {chunk_json[:100]}...")
                    # 将其作为text_chunk处理
                    text_chunk_payload = {"chunk": chunk_json}
                    protocol_message = create_protocol_message("text_chunk", text_chunk_payload, response_message_id)
                    await connection_manager.send_personal_message(protocol_message, client_id)

            # 发送 stream_end 事件
            end_message = create_stream_end_message(response_message_id)
            await connection_manager.send_personal_message(end_message, client_id)

    except WebSocketDisconnect:
        connection_manager.disconnect(client_id)
        logger.info(f"Client {client_id} disconnected.")
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        # 发送结构化错误消息
        error_message = create_error_message(f"抱歉，处理你的请求时出现了错误：{str(e)}")
        await connection_manager.send_personal_message(error_message, client_id)
        connection_manager.disconnect(client_id)
