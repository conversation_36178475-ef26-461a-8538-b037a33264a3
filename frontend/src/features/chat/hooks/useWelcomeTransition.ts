// src/features/chat/hooks/useWelcomeTransition.ts
// 欢迎页过渡动画Hook - 管理从欢迎状态到对话状态的过渡逻辑
import { useMemo } from 'react';

interface WelcomeTransition {
  /** 是否应显示欢迎屏幕 */
  isWelcomeScreen: boolean;
  /** 传递给 motion.footer 的动画属性 */
  footerMotionProps: {
    layout: boolean;
    initial: {
      y: string | number;
      opacity: number;
      scale: number;
    };
    animate: {
      y: number;
      opacity: number;
      scale: number;
    };
    exit: {
      opacity: number;
      scale: number;
    };
    transition: {
      duration: number;
      ease: number[];
      type: string;
      stiffness: number;
      damping: number;
    };
  };
}

/**
 * useWelcomeTransition Hook - 重构版欢迎页过渡动画管理
 *
 * 核心重构：
 * 1. 移除对messagesCount的依赖，改为基于hasStarted状态
 * 2. 动画的触发由组件挂载/卸载控制，而不是状态变化
 * 3. 使用spring动画，提供更自然的物理效果
 * 4. 动画属性为静态定义，触发由AnimatePresence管理
 *
 * 解决的问题：
 * - 修复动画时机与真正意图不匹配的问题
 * - 避免输入框闪烁和视觉脱节
 * - 统一动画控制权，避免与滚动逻辑冲突
 *
 * @param hasStarted 是否已开始对话
 * @returns 包含欢迎屏幕状态和footer动画属性的对象
 */
export const useWelcomeTransition = (hasStarted: boolean): WelcomeTransition => {
  // 计算是否显示欢迎屏幕
  const isWelcomeScreen = !hasStarted;

  // 计算footer动画属性 - 静态定义，由AnimatePresence控制触发
  const footerMotionProps = useMemo(() => {
    return {
      layout: true,
      // 初始状态（当它作为对话界面的一部分首次出现时）
      initial: {
        y: "calc(-50vh + 4rem)", // 稍微调整，使其更居中
        opacity: 0,
        scale: 0.95
      },
      // 动画目标状态
      animate: {
        y: 0,
        opacity: 1,
        scale: 1
      },
      // 退出动画（如果需要的话）
      exit: {
        opacity: 0,
        scale: 0.95
      },
      transition: {
        duration: 0.8, // 增加动画时长，使其更平滑、更有仪式感
        ease: [0.22, 1, 0.36, 1] as number[],
        type: "spring", // 使用spring动画，物理效果更自然
        stiffness: 100,
        damping: 20,
      }
    };
  }, []); // 依赖项数组为空，因为动画属性是固定的。动画的触发由组件挂载决定。

  return {
    isWelcomeScreen,
    footerMotionProps
  };
};
