// src/features/chat/hooks/useWelcomeTransition.ts
// 欢迎页过渡动画Hook - 管理从欢迎状态到对话状态的过渡逻辑
import { useMemo } from 'react';

interface WelcomeTransition {
  /** 是否应显示欢迎屏幕 */
  isWelcomeScreen: boolean;
  /** 传递给 motion.footer 的动画属性 */
  footerMotionProps: {
    layout: boolean;
    initial: {
      y: string | number;
      opacity: number;
      scale: number;
    };
    animate: {
      y: number;
      opacity: number;
      scale: number;
    };
    transition: {
      duration: number;
      ease: number[];
      type: string;
      delay: number;
    };
  };
}

/**
 * useWelcomeTransition Hook - 修复版欢迎页过渡动画管理
 *
 * 核心修复：
 * 1. 恢复基于messagesCount的精确时机控制
 * 2. 只在第一条AI消息时执行动画，避免闪烁
 * 3. 优化动画参数，确保平滑过渡
 *
 * 解决的问题：
 * - 修复输入框闪烁问题
 * - 确保动画时机与消息状态同步
 * - 避免与自动滚动逻辑冲突
 *
 * @param hasStarted 是否已开始对话
 * @param messagesCount 消息总数
 * @returns 包含欢迎屏幕状态和footer动画属性的对象
 */
export const useWelcomeTransition = (hasStarted: boolean, messagesCount: number): WelcomeTransition => {
  // 计算是否显示欢迎屏幕
  const isWelcomeScreen = !hasStarted;

  // 计算footer动画属性 - 恢复精确的时机控制
  const footerMotionProps = useMemo(() => {
    // 关键修复：只在第一条消息时执行动画，避免闪烁
    const shouldAnimate = messagesCount === 1;

    return {
      layout: true,
      initial: {
        y: shouldAnimate ? "calc(-40vh + 2rem)" : 0,
        opacity: shouldAnimate ? 0.9 : 1,
        scale: shouldAnimate ? 0.98 : 1
      },
      animate: {
        y: 0,
        opacity: 1,
        scale: 1
      },
      transition: {
        duration: 0.6, // 恢复合理的动画时长
        ease: [0.22, 1, 0.36, 1] as number[], // 使用之前验证过的缓动函数
        type: "tween" as const,
        delay: 0
      }
    };
  }, [messagesCount]);

  return {
    isWelcomeScreen,
    footerMotionProps
  };
};
