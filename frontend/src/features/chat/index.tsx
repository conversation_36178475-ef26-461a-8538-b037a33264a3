// src/features/chat/index.tsx
import { useEffect, useRef, useCallback } from 'react';
import { useChat } from './hooks/useChat';
import { useAutoScroll } from '@/hooks/useAutoScroll';
import { useWelcomeTransition } from './hooks/useWelcomeTransition';
import { LogStream } from './components/LogStream';
import { WelcomeScreen } from './components/WelcomeScreen';
import { AnimatedFooter } from './components/AnimatedFooter';
import { ScrollToBottomButton } from './components/ScrollToBottomButton';
import { ConnectionStatus } from '@/components/ui/ConnectionStatus';

export const ChatView = () => {
  const { messages, sendMessage, isThinking, hasStarted } = useChat();
  const footerRef = useRef<HTMLDivElement>(null);

  // 使用欢迎页过渡动画Hook - 传递messagesCount确保精确的动画时机
  const { isWelcomeScreen, footerMotionProps } = useWelcomeTransition(hasStarted, messages.length);

  // 使用自动滚动hook
  const {
    scrollContainerRef,
    showScrollButton,
    userHasScrolled,
    scrollToBottom,
    isAtBottom,
    // updateScrollState // 暂时未使用
  } = useAutoScroll({
    threshold: 100,
    debug: false // 可以设置为true来启用调试日志
  });

  // 智能滚动逻辑：发送消息后立即滚动到底部
  const handleSendMessage = useCallback((message: string) => {
    sendMessage(message);
    // 发送消息后滚动到底部（使用微妙的平滑动画）
    setTimeout(() => {
      scrollToBottom('smooth');
    }, 50);
  }, [sendMessage, scrollToBottom]);

  // 监听消息变化，实现智能滚动
  useEffect(() => {
    if (messages.length === 0) return;

    const lastMessage = messages[messages.length - 1];

    // 如果是AI消息且正在思考或流式输出，只有在用户处于底部时才自动滚动
    if (lastMessage.role === 'xi' && (isThinking || lastMessage.content)) {
      if (!userHasScrolled && isAtBottom()) {
        scrollToBottom('smooth');
      }
    }
  }, [messages, isThinking, userHasScrolled, isAtBottom, scrollToBottom]);

  if (isWelcomeScreen) {
    return (
      <WelcomeScreen
        onSendMessage={handleSendMessage}
        disabled={isThinking}
      />
    );
  }

  // 对话状态：正常布局
  return (
    <div className="h-screen bg-background text-foreground font-sans relative">
      {/* 滚动区域 - 只包含消息内容 */}
      <div
        ref={scrollContainerRef}
        className="h-full pb-32 overflow-y-auto"
        style={{ 
          scrollBehavior: 'auto' // 移除CSS的smooth，由JS控制
        }}
      >
        <div className="flex justify-center">
          <LogStream messages={messages} isThinking={isThinking} />
        </div>
      </div>

      {/* 固定在底部的输入框 - 完全独立 */}
      <div
        ref={footerRef}
        className="absolute bottom-0 left-0 right-0"
      >
        <AnimatedFooter
          onSendMessage={handleSendMessage}
          disabled={isThinking}
          motionProps={footerMotionProps}
        />
      </div>

      {/* 滚动到底部按钮 */}
      <ScrollToBottomButton
        show={showScrollButton}
        onClick={() => scrollToBottom('smooth')}
      />

      {/* WebSocket连接状态指示器 */}
      <ConnectionStatus />
    </div>
  );
};