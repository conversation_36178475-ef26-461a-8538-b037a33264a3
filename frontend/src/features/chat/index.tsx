// src/features/chat/index.tsx
import { useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useChat } from './hooks/useChat';
import { useAutoScroll } from '@/hooks/useAutoScroll';
import { useWelcomeTransition } from './hooks/useWelcomeTransition';
import { LogStream } from './components/LogStream';
import { WelcomeScreen } from './components/WelcomeScreen';
import { AnimatedFooter } from './components/AnimatedFooter';
import { ScrollToBottomButton } from './components/ScrollToBottomButton';
import { ConnectionStatus } from '@/components/ui/ConnectionStatus';

export const ChatView = () => {
  const { messages, sendMessage, isThinking, hasStarted } = useChat();
  const footerRef = useRef<HTMLDivElement>(null);

  // 使用欢迎页过渡动画Hook - 重构后只需要hasStarted参数
  const { isWelcomeScreen, footerMotionProps } = useWelcomeTransition(hasStarted);

  // 使用自动滚动hook
  const {
    scrollContainerRef,
    showScrollButton,
    userHasScrolled,
    scrollToBottom,
    // isAtBottom, // 简化后不再需要
    // updateScrollState // 暂时未使用
  } = useAutoScroll({
    threshold: 100,
    debug: false // 可以设置为true来启用调试日志
  });

  // 简化的消息发送处理 - 移除滚动逻辑，统一由useEffect管理
  const handleSendMessage = useCallback((message: string) => {
    sendMessage(message);
    // 移除这里的滚动代码，让useEffect统一处理滚动
  }, [sendMessage]);

  // 统一的滚动逻辑 - 监听消息变化，实现智能滚动
  useEffect(() => {
    if (messages.length === 0) return;

    // 简化的滚动决策：如果用户没有向上滚动，则始终滚动到底部
    // 这覆盖了用户发送消息和AI开始响应的两种情况
    if (!userHasScrolled) {
      scrollToBottom('smooth');
    }
  }, [messages, userHasScrolled, scrollToBottom]);

  // 使用AnimatePresence管理WelcomeScreen和对话界面的过渡
  return (
    <div className="h-screen bg-background text-foreground font-sans relative overflow-hidden">
      <AnimatePresence>
        {isWelcomeScreen ? (
          <WelcomeScreen
            key="welcome" // key是AnimatePresence正确工作的关键
            onSendMessage={handleSendMessage}
            disabled={isThinking}
          />
        ) : (
          <motion.div
            key="chat"
            className="h-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {/* 滚动区域 - 只包含消息内容 */}
            <div
              ref={scrollContainerRef}
              className="h-full pb-32 overflow-y-auto"
              style={{
                scrollBehavior: 'auto' // 移除CSS的smooth，由JS控制
              }}
            >
              <div className="flex justify-center">
                <LogStream messages={messages} isThinking={isThinking} />
              </div>
            </div>

            {/* 固定在底部的输入框 - 完全独立 */}
            <div
              ref={footerRef}
              className="absolute bottom-0 left-0 right-0"
            >
              <AnimatedFooter
                onSendMessage={handleSendMessage}
                disabled={isThinking}
                motionProps={footerMotionProps}
              />
            </div>

            {/* 滚动到底部按钮 */}
            <ScrollToBottomButton
              show={showScrollButton}
              onClick={() => scrollToBottom('smooth')}
            />

            {/* WebSocket连接状态指示器 */}
            <ConnectionStatus />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};