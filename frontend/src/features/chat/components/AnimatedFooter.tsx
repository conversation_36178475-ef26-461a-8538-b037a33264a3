// src/features/chat/components/AnimatedFooter.tsx
// 纯UI动画输入框容器 - 接收动画属性并渲染
import React from 'react';
import { motion } from 'framer-motion';
import { ChatInput } from './ChatInput';

interface AnimatedFooterProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  /** 从useWelcomeTransition Hook传入的动画属性 */
  motionProps: any;
}

/**
 * AnimatedFooter - 纯UI组件
 *
 * 核心职责：
 * 1. 接收动画属性并应用到motion.footer
 * 2. 渲染ChatInput组件
 * 3. 保持样式和布局的一致性
 *
 * 架构特点：
 * - 完全"dumb"组件，不包含任何动画逻辑
 * - 通过props接收所有必要的配置
 * - 专注于渲染，易于测试和维护
 */
export const AnimatedFooter: React.FC<AnimatedFooterProps> = ({
  onSendMessage,
  disabled = false,
  motionProps
}) => {
  return (
    <motion.footer
      {...motionProps}
      className="w-full p-6 bg-transparent"
    >
      <ChatInput
        onSendMessage={onSendMessage}
        disabled={disabled}
        isInitialState={false}
      />
    </motion.footer>
  );
};
