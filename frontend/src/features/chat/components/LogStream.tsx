// src/features/chat/components/LogStream.tsx
// 日志流组件 - 使用标准容器宽度
import React from 'react';
import type { Message } from '../types';
import { ChatMessage } from './ChatMessage';

interface LogStreamProps {
  messages: Message[];
  isThinking: boolean;
}

export const LogStream: React.FC<LogStreamProps> = ({ messages, isThinking }) => {
  return (
    <div className="w-full max-w-3xl mx-auto px-4">
      {messages.map((message, index) => (
        <ChatMessage
          key={message.id}
          message={message}
          isLastMessage={index === messages.length - 1}
          isThinking={isThinking}
        />
      ))}
    </div>
  );
};
