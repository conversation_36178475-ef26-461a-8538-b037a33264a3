// tests/integration.spec.ts
// 任务V0.1-4端到端集成测试

import { test, expect } from '@playwright/test';

test.describe('任务V0.1-4: 双模式集成测试', () => {
  
  test.describe('模拟模式测试', () => {
    test.beforeEach(async ({ page }) => {
      // 设置模拟模式
      await page.addInitScript(() => {
        localStorage.setItem('VITE_USE_MOCK_DATA', 'true');
      });
      await page.goto('/');
    });

    test('TC-1: 基础文本响应测试', async ({ page }) => {
      // 等待欢迎界面加载
      await expect(page.locator('[data-testid="welcome-screen"]')).toBeVisible();
      
      // 发送消息 "1"
      const input = page.locator('textarea[placeholder*="输入"]');
      await input.fill('1');
      await input.press('Enter');

      // 验证状态指示器显示模拟模式
      await expect(page.locator('text=🎭 模拟模式')).toBeVisible();

      // 验证消息出现在日志流中
      await expect(page.locator('text=**基础文本响应测试**')).toBeVisible({ timeout: 10000 });
      
      // 验证思考动画消失
      await expect(page.locator('[data-testid="thinking-indicator"]')).not.toBeVisible({ timeout: 15000 });
    });

    test('TC-2: 工具调用模拟测试', async ({ page }) => {
      // 等待欢迎界面加载
      await expect(page.locator('[data-testid="welcome-screen"]')).toBeVisible();
      
      // 发送消息 "2"
      const input = page.locator('textarea[placeholder*="输入"]');
      await input.fill('2');
      await input.press('Enter');

      // 验证工具调用提示出现
      await expect(page.locator('text=正在使用web_search')).toBeVisible({ timeout: 5000 });
      
      // 验证工具调用后的文本内容
      await expect(page.locator('text=**工具调用模拟测试**')).toBeVisible({ timeout: 10000 });
    });

    test('TC-3: 流式输出效果验证', async ({ page }) => {
      // 监听控制台日志以验证协议事件
      const logs: string[] = [];
      page.on('console', msg => {
        if (msg.text().includes('Received event:')) {
          logs.push(msg.text());
        }
      });

      await expect(page.locator('[data-testid="welcome-screen"]')).toBeVisible();
      
      const input = page.locator('textarea[placeholder*="输入"]');
      await input.fill('1');
      await input.press('Enter');

      // 等待响应完成
      await expect(page.locator('text=界面渲染效果看起来不错')).toBeVisible({ timeout: 15000 });

      // 验证协议事件流
      expect(logs.some(log => log.includes('stream_start'))).toBeTruthy();
      expect(logs.some(log => log.includes('text_chunk'))).toBeTruthy();
      expect(logs.some(log => log.includes('stream_end'))).toBeTruthy();
    });
  });

  test.describe('真实模式测试 (需要后端)', () => {
    test.beforeEach(async ({ page }) => {
      // 设置真实模式
      await page.addInitScript(() => {
        localStorage.setItem('VITE_USE_MOCK_DATA', 'false');
      });
      await page.goto('/');
    });

    test('TC-4: WebSocket连接状态验证', async ({ page }) => {
      // 验证连接状态指示器出现
      const statusIndicator = page.locator('[data-testid="connection-status"]');
      
      // 可能显示连接中或已连接状态
      await expect(statusIndicator.or(
        page.locator('text=🔄 连接中'),
        page.locator('text=🔗 已连接')
      )).toBeVisible({ timeout: 10000 });
    });

    test('TC-5: 真实对话测试', async ({ page }) => {
      // 等待连接建立
      await page.waitForTimeout(2000);
      
      await expect(page.locator('[data-testid="welcome-screen"]')).toBeVisible();
      
      const input = page.locator('textarea[placeholder*="输入"]');
      await input.fill('你好');
      await input.press('Enter');

      // 验证消息发送成功
      await expect(page.locator('text=你好')).toBeVisible();
      
      // 验证AI响应（如果后端可用）
      // 注意：这个测试可能会因为后端不可用而失败
      await expect(page.locator('[data-testid="thinking-indicator"]')).toBeVisible({ timeout: 5000 });
    });
  });

  test.describe('UI设计原则验证', () => {
    test.beforeEach(async ({ page }) => {
      await page.addInitScript(() => {
        localStorage.setItem('VITE_USE_MOCK_DATA', 'true');
      });
      await page.goto('/');
    });

    test('TC-6: 连接状态指示器设计验证', async ({ page }) => {
      const input = page.locator('textarea[placeholder*="输入"]');
      await input.fill('1');
      await input.press('Enter');

      // 验证状态指示器使用标准材质
      const statusDiv = page.locator('text=🎭 模拟模式').locator('..');
      
      // 验证背景模糊效果
      await expect(statusDiv).toHaveClass(/backdrop-blur-xl/);
      
      // 验证边框样式
      await expect(statusDiv).toHaveClass(/border-border/);
      
      // 验证阴影效果
      await expect(statusDiv).toHaveClass(/shadow-lg/);
    });

    test('TC-7: 灰度色彩验证', async ({ page }) => {
      // 验证页面不包含彩色元素
      const coloredElements = await page.locator('[style*="color: rgb"], [class*="text-red"], [class*="text-green"], [class*="text-blue"], [class*="bg-red"], [class*="bg-green"], [class*="bg-blue"]').count();
      expect(coloredElements).toBe(0);
    });
  });
});
